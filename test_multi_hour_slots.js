// Simple test to verify multi-hour slot grouping logic
// This tests the core functionality without requiring the full React testing environment

// Mock time slots data - simulating a 2-hour consultation from 14:30-16:20
const mockTimeSlots = [
  {
    id: '1',
    faculty_time_slot_id: 'fts_1',
    datetime: '2024-01-15T14:30:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '2', 
    faculty_time_slot_id: 'fts_1', // Same faculty_time_slot_id for grouping
    datetime: '2024-01-15T15:00:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '3',
    faculty_time_slot_id: 'fts_1', // Same faculty_time_slot_id for grouping
    datetime: '2024-01-15T15:30:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '4',
    faculty_time_slot_id: 'fts_1', // Same faculty_time_slot_id for grouping
    datetime: '2024-01-15T16:00:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '5',
    faculty_time_slot_id: 'fts_2', // Different faculty_time_slot_id - separate slot
    datetime: '2024-01-15T16:30:00Z',
    is_available: true,
    is_booked: false
  }
]

// Replicate the grouping logic from the component
function getSlotGroups(date, timeSlots) {
  const dateStr = date.toISOString().split('T')[0]
  const slotsForDate = timeSlots.filter(slot => {
    const slotDate = new Date(slot.datetime)
    return slotDate.toISOString().split('T')[0] === dateStr
  })

  // Group by faculty_time_slot_id
  const groupedSlots = slotsForDate.reduce((groups, slot) => {
    const key = slot.faculty_time_slot_id || slot.id
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(slot)
    return groups
  }, {})

  // Sort each group by datetime
  Object.keys(groupedSlots).forEach(key => {
    groupedSlots[key].sort((a, b) => new Date(a.datetime) - new Date(b.datetime))
  })

  return groupedSlots
}

function shouldHideSlot(date, slot, timeSlots) {
  const groups = getSlotGroups(date, timeSlots)
  const groupKey = slot.faculty_time_slot_id || slot.id
  const group = groups[groupKey]
  
  if (!group || group.length <= 1) return false
  
  // Hide if not the first slot in the group
  return group[0].id !== slot.id
}

function getSlotGroupSpan(date, slot, timeSlots) {
  const groups = getSlotGroups(date, timeSlots)
  const groupKey = slot.faculty_time_slot_id || slot.id
  const group = groups[groupKey]
  
  return group ? group.length : 1
}

function formatTimeRange(startSlot, endSlot) {
  const startTime = new Date(startSlot.datetime)
  const endTime = new Date(endSlot.datetime)

  // Add 30 minutes to end time since each slot is 30 minutes
  endTime.setMinutes(endTime.getMinutes() + 30)

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'UTC'  // Use UTC to match the input data
    })
  }

  return `${formatTime(startTime)} - ${formatTime(endTime)}`
}

// Test the functionality
console.log('Testing multi-hour slot grouping logic...\n')

const testDate = new Date('2024-01-15')
const groups = getSlotGroups(testDate, mockTimeSlots)

console.log('Grouped slots:', groups)
console.log('')

// Test each slot
mockTimeSlots.forEach((slot, index) => {
  const shouldHide = shouldHideSlot(testDate, slot, mockTimeSlots)
  const groupSpan = getSlotGroupSpan(testDate, slot, mockTimeSlots)
  
  console.log(`Slot ${index + 1} (${slot.id}):`)
  console.log(`  Faculty Time Slot ID: ${slot.faculty_time_slot_id}`)
  console.log(`  DateTime: ${slot.datetime}`)
  console.log(`  Should Hide: ${shouldHide}`)
  console.log(`  Group Span: ${groupSpan}`)
  
  if (!shouldHide && groupSpan > 1) {
    const group = groups[slot.faculty_time_slot_id]
    const timeRange = formatTimeRange(group[0], group[group.length - 1])
    console.log(`  Time Range: ${timeRange}`)
  }
  console.log('')
})

// Expected results:
console.log('Expected Results:')
console.log('- Slot 1: Should NOT be hidden, span = 4 (first in group fts_1)')
console.log('- Slot 2: Should be hidden (part of group fts_1)')
console.log('- Slot 3: Should be hidden (part of group fts_1)')
console.log('- Slot 4: Should be hidden (part of group fts_1)')
console.log('- Slot 5: Should NOT be hidden, span = 1 (single slot in group fts_2)')
console.log('- Multi-hour slot should show time range: 14:30 - 16:30')
