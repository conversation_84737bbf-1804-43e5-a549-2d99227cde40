import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import FacultyCalendarModal from '../FacultyCalendarModal'

// Mock the CSS module
jest.mock('../FacultyCalendarModal.module.css', () => ({
  timeSlotGrid: 'timeSlotGrid',
  timeLabel: 'timeLabel',
  dateColumn: 'dateColumn',
  multiHourSlot: 'multiHourSlot'
}))

// Mock InstUI components
jest.mock('@instructure/ui-modal', () => ({
  Modal: ({ children, open, onDismiss, label }: any) => 
    open ? <div data-testid="modal" aria-label={label}>{children}</div> : null
}))

jest.mock('@instructure/ui-flex', () => ({
  Flex: ({ children, direction, ...props }: any) => 
    <div data-testid="flex" style={{ flexDirection: direction }} {...props}>{children}</div>,
  'Flex.Item': ({ children, ...props }: any) => 
    <div data-testid="flex-item" {...props}>{children}</div>
}))

jest.mock('@instructure/ui-view', () => ({
  View: ({ children, as = 'div', ...props }: any) => 
    React.createElement(as, { 'data-testid': 'view', ...props }, children)
}))

jest.mock('@instructure/ui-buttons', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => 
    <button data-testid="time-slot-button" onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>,
  CloseButton: ({ onClick, ...props }: any) => 
    <button data-testid="close-button" onClick={onClick} {...props}>Close</button>
}))

jest.mock('@instructure/ui-text', () => ({
  Text: ({ children, ...props }: any) => 
    <span data-testid="text" {...props}>{children}</span>
}))

jest.mock('@instructure/ui-heading', () => ({
  Heading: ({ children, level, ...props }: any) => 
    React.createElement(`h${level}`, { 'data-testid': 'heading', ...props }, children)
}))

// Sample test data
const mockTimeSlots = [
  {
    id: '1',
    faculty_time_slot_id: 'fts_1',
    datetime: '2024-01-15T14:30:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '2', 
    faculty_time_slot_id: 'fts_1', // Same faculty_time_slot_id for grouping
    datetime: '2024-01-15T15:00:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '3',
    faculty_time_slot_id: 'fts_1', // Same faculty_time_slot_id for grouping
    datetime: '2024-01-15T15:30:00Z',
    is_available: true,
    is_booked: false
  },
  {
    id: '4',
    faculty_time_slot_id: 'fts_2', // Different faculty_time_slot_id
    datetime: '2024-01-15T16:00:00Z',
    is_available: true,
    is_booked: false
  }
]

const mockFaculty = {
  id: '1',
  name: 'Dr. Test Faculty',
  email: '<EMAIL>'
}

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  faculty: mockFaculty,
  timeSlots: mockTimeSlots,
  onTimeSlotSelect: jest.fn(),
  selectedTimeSlot: null
}

describe('FacultyCalendarModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the modal when open', () => {
    render(<FacultyCalendarModal {...defaultProps} />)
    
    expect(screen.getByTestId('modal')).toBeInTheDocument()
    expect(screen.getByText('Dr. Test Faculty - Available Time Slots')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<FacultyCalendarModal {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument()
  })

  it('groups consecutive time slots with same faculty_time_slot_id', () => {
    render(<FacultyCalendarModal {...defaultProps} />)
    
    // Should render time slot buttons
    const buttons = screen.getAllByTestId('time-slot-button')
    
    // We expect 2 buttons: one for the multi-hour slot (fts_1) and one for the single slot (fts_2)
    // The multi-hour slot should span 3 time periods but only show 1 button
    expect(buttons).toHaveLength(2)
  })

  it('calls onTimeSlotSelect when a time slot is clicked', () => {
    const onTimeSlotSelect = jest.fn()
    render(<FacultyCalendarModal {...defaultProps} onTimeSlotSelect={onTimeSlotSelect} />)
    
    const buttons = screen.getAllByTestId('time-slot-button')
    buttons[0].click()
    
    expect(onTimeSlotSelect).toHaveBeenCalledWith(mockTimeSlots[0])
  })

  it('calls onClose when close button is clicked', () => {
    const onClose = jest.fn()
    render(<FacultyCalendarModal {...defaultProps} onClose={onClose} />)
    
    const closeButton = screen.getByTestId('close-button')
    closeButton.click()
    
    expect(onClose).toHaveBeenCalled()
  })

  it('displays time range for multi-hour slots', () => {
    render(<FacultyCalendarModal {...defaultProps} />)
    
    // Should show the time range for the multi-hour slot
    expect(screen.getByText('14:30 - 16:00')).toBeInTheDocument()
  })

  it('handles empty time slots array', () => {
    render(<FacultyCalendarModal {...defaultProps} timeSlots={[]} />)
    
    expect(screen.getByTestId('modal')).toBeInTheDocument()
    expect(screen.queryByTestId('time-slot-button')).not.toBeInTheDocument()
  })
})
