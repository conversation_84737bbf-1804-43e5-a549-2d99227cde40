/* Faculty Calendar Modal Styles */

.calendarGrid {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.weekHeader {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.timeSlotRow {
  border-bottom: 1px solid #f1f3f5;
  min-height: 40px;
}

.timeSlotRow:last-child {
  border-bottom: none;
}

.timeLabel {
  background-color: #f8f9fa;
  border-right: 1px solid #e1e5e9;
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.timeSlotCell {
  border-right: 1px solid #f1f3f5;
  padding: 2px;
  min-height: 40px;
  position: relative;
}

.timeSlotCell:last-child {
  border-right: none;
}

.timeSlot {
  width: 100%;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: default;
}

.timeSlot.available {
  background-color: #10b981;
  color: white;
  cursor: pointer;
}

.timeSlot.available:hover {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.timeSlot.booked {
  background-color: #ef4444;
  color: white;
}

.timeSlot.selected {
  background-color: #3b82f6;
  color: white;
  box-shadow: 0 0 0 2px #93c5fd;
}

.timeSlot.past {
  background-color: #e5e7eb;
  color: #9ca3af;
}

.timeSlot.unavailable {
  background-color: #f3f4f6;
  color: #d1d5db;
}

.legend {
  display: flex;
  gap: 16px;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
  flex-wrap: wrap;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legendColor {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legendColor.available {
  background-color: #10b981;
}

.legendColor.booked {
  background-color: #ef4444;
}

.legendColor.selected {
  background-color: #3b82f6;
}

.legendColor.past {
  background-color: #e5e7eb;
}

.navigationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
}

.weekNavigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.monthYearSelector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.calendarContainer {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
}

.loadingContainer {
  padding: 32px;
  text-align: center;
  color: #6b7280;
}

.dayHeader {
  padding: 8px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  background-color: #f8f9fa;
  border-right: 1px solid #e1e5e9;
}

.dayHeader:last-child {
  border-right: none;
}

.modalHeader {
  border-bottom: 1px solid #e1e5e9;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.modalBody {
  padding: 16px;
}

.modalFooter {
  border-top: 1px solid #e1e5e9;
  padding-top: 16px;
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .navigationHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .weekNavigation {
    justify-content: center;
  }
  
  .monthYearSelector {
    justify-content: center;
  }
  
  .legend {
    gap: 12px;
  }
  
  .timeSlot {
    font-size: 10px;
    height: 28px;
  }
  
  .timeLabel {
    font-size: 11px;
    min-height: 28px;
  }
  
  .timeSlotCell {
    min-height: 28px;
  }
}

/* Accessibility improvements */
.timeSlot:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.timeSlot.available:focus {
  outline-color: #10b981;
}

/* Animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loadingSlot {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: #f3f4f6;
}

/* Multi-hour time slot styles */
.multiHourSlot {
  position: absolute !important;
  top: 4px !important;
  left: 4px !important;
  right: 4px !important;
  z-index: 10 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.multiHourSlot.available {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.multiHourSlot.booked {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.multiHourSlot.selected {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 0 0 3px #93c5fd, 0 2px 8px rgba(0, 0, 0, 0.15);
}

.slotTimeRange {
  font-size: 10px;
  opacity: 0.9;
  margin-top: 2px;
  font-weight: normal;
  line-height: 1.2;
}
